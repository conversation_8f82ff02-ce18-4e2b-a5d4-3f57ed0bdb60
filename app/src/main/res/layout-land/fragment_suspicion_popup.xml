<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="24dp"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Make a Suspicion"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/purple_500"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/suspectSpinner"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:theme="@style/SpinnerBoldStyle"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Spinner
            android:id="@+id/roomSpinner"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:layout_marginTop="8dp"
            android:theme="@style/SpinnerBoldStyle"
            app:layout_constraintTop_toBottomOf="@id/suspectSpinner"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Spinner
            android:id="@+id/weaponSpinner"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:layout_marginTop="8dp"
            android:theme="@style/SpinnerBoldStyle"
            app:layout_constraintTop_toBottomOf="@id/roomSpinner"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/button_make_suspicion"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="Make a Suspicion"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:backgroundTint="@color/purple_500"
            android:textColor="@color/white"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="8dp"
            app:layout_constraintTop_toBottomOf="@id/weaponSpinner"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/button_cancel"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="Cancel"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:backgroundTint="@color/teal_200"
            android:textColor="@color/black"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/button_make_suspicion"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
