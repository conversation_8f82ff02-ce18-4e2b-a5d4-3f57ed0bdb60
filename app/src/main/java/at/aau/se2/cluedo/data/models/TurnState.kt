package at.aau.se2.cluedo.data.models

enum class TurnState(val value: String) {
    PLAYERS_TURN_ROLL_DICE("PLAYERS_TURN_ROLL_DICE"),
    PLAYERS_TURN_MOVE("PLAYERS_TURN_MOVE"),
    PLAYERS_TURN_SUGGEST("PLAYERS_TURN_SUGGEST"),
    PLAYERS_TURN_SOLVE("PLAYERS_TURN_SOLVE"),
    PLAYERS_TURN_END("PLAYERS_TURN_END"),
    PLAYER_HAS_WON("PLAYER_HAS_WON"),
    WAITING_FOR_PLAYERS("WAITING_FOR_PLAYERS"),
    WAITING_FOR_START("WAITING_FOR_START"),
    GAME_ENDED("GAME_ENDED")
}
